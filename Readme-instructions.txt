The Dual N-Back exercise featured in Brain Workshop was the subject of 
an April 2008 peer-reviewed scientific study which shows that 
practicing the Dual N-Back task for 20 minutes 4-5 days per week will 
improve your working memory (short term memory) and fluid intelligence. 

An N-Back task is where you have a stream of stimuli, such as a 
sequence of letters, and you have to identify when a stimulus is the
same as the one presented N trials ago.  A Dual N-Back task is when
you have two separate streams of stimuli to handle simultaneously.

If you've never tried Dual N-Back before, here's a quick tutorial to 
get you started.

Dual 1-Back:

It's best to begin with Dual 1-Back, the simplest mode.

    Launch Brain Workshop.
    Press Space to enter the Workshop.
    Press M to switch to Manual mode.
    Press F1 to decrease the N-back level to 1.
    Press Space to begin a Dual 1-Back session. Each session is about 
    1 minute in duration.

You will see a blue square appear every 3 seconds accompanied by the 
sound of a letter. If you don't hear any sound, make sure your 
speakers are not muted.

    Press A (position match) if the POSITION of the blue square is the
    SAME as it was 1 trial back (i.e., the square appears in the same
    position twice in a row).

    Press L (letter match) if the LETTER you hear is the SAME it was 1 
    trial back (i.e., you hear the same letter twice in a row).

The Dual part of the name of this game mode, Dual 1-Back, comes from 
the fact that you are remembering two different stimuli -- the 
square's position, and the letter played through the speakers. The 
1-Back part indicates how many trials back you're being asked to
remember to decide whether that trial's position or letter match the 
current position or letter.

It's easy to perform this task when focusing only on a single cue 
(either the square's position or the letter). The challenging part is 
to do both at the same time!

Dual 2-Back

Brain Workshop starts in Dual 2-Back mode by default. Dual 2-Back is 
significantly more difficult than Dual 1-Back. Don't be discouraged --
learning and practicing this exercise at the limit of your ability is 
what increases your fluid intelligence!

    Ensure that the game mode is Dual 2-Back. (if not, use F1 and F2 to
    adjust the N-Back level. Note: these keys will only function in 
    Manual mode.)
    
    Press Space to begin a Dual 2-Back session.

This time, you will need to remember the square's position and the 
voiced letter from 2 trials back instead of 1 trial back.

    Press A (position match) if the POSITION of the blue square is the 
    SAME as it was 2 trials back (i.e., the square appears in the same 
    position as it did two trials ago).

    Press L (letter match) if the LETTER you hear is the SAME it was 2 
    trials back (i.e., you hear the same letter as you did two trials 
    ago).

If you find this too difficult at first, try focusing on only one of 
the cues (either the square's position or the letter).

Here's an example. Ignore the square's position for now and focus on 
the letters you hear. Suppose the first letter is "A" and the second 
is "B". Now it's the third trial and the letter is "A" again.

Since the current letter, A, is the same as the letter from two trials 
ago (i.e., the first trial), you've found a match and press the L key. 
Now suppose that on the next trial the letter is "B". You press L again
because this word is also the same as it was two trials ago.

Here's another example.

    Trial 1: C
    Trial 2: R
    Trial 3: T
    Trial 4: R (this is a 2-back match)
    Trial 5: T (this is a 2-back match)
    Trial 6: T
    Trial 7: S
    Trial 8: C
    Trial 9: S (this is a 2-back match)

When you get the hang of hearing a match from 2 trials back, try 
shifting your focus to the square's position. Then try to do both at 
the same time.

Since you switched to Manual mode at the beginning, Brain Workshop will
not change the n-back level automatically. Press M to exit Manual mode. 
Now the n-back level will be adjusted for you automatically, to ensure 
you're always playing at the right level. Once you achieve a high 
enough score in Dual 2-Back, you'll be bumped up to the next level -- 
Dual 3-Back. Challenge yourself and try to achieve the highest level 
possible!

If you find that Dual N-Back isn't challenging enough, Brain Workshop 
has many additional modes which provide an extra challenge.  These 
include Triple, Quadruple, and Pentuple N-back with color, image,
and dual (stereo) auditory stimuli, combination modes, arithmetic 
modes, variable n-back modes, "crab-back" modes, multiple-visual-
stimulus modes, and high-interference modes.

Triple, Quadruple, and Pentuple N-back modes are just like the regular
Dual N-Back mode except that instead of there being only two cues
(position and letter), there are up to five (position, color, image,
letter 1, letter 2).

Combination modes add another layer of complexity to the task.  In 
most of the game modes, each cue modality is independent of and 
unrelated to the others.  However, in combination modes, in each
trial a letter is both played on the speakers and displayed on the 
screen, and you not only have to identify if the audio cue matches 
the audio cue N trials ago (and the same for the visual cue), but 
you also have to identify when the audio cue matches the *visual*
cue N trials ago (and vice versa).  

With arithmetic modes, you have to perform arithmetic calculations on
two numbers, A and B.  The operation varies each trial and the 
current operation is played on the speakers.  The number A is the
number that was displayed N trials ago, and B is the number currently 
displayed.  For subtraction and division, the order is (A - B) and 
(A / B).

Variable N-back modes are similar to the other modes, but instead of
having a fixed value for N for the entire session, it varies between
trials.  During each trial, the current value of N is displayed on the
screen.  The value of N for any is never greater than the difficulty
level for the current session.

Crab-back modes force you to mentally reverse each block of N stimuli
when looking for a match.  Take this example in Crab Audio 3-Back: 

TrialNumber:	123 456 789 012 345 678
CurrentStim:	TCH HRT KTT LLK TQK LQK
MatchVersus:	___ HCT TRH TTK KLL KQT
IsItAMatch?:	___ *-* --- --* --- -*-

You can also think of this as having a variable value of N for each 
trial, but instead of having that value of N be random and displayed 
for each trial like in Variable N-Back modes, it's implicit and 
follows a regular formula.  In 3-Back, the value of N cycles 5, 3, 1,
5, 3, 1, etc.  In 4-Back, it cycles 7, 5, 3, 1, etc. 

In multiple-stimulus modes, instead of an object being displayed in 
one position per trial, objects are displayed in two, three, or four
positions simultaneously.  The objects displayed are not identical,
so not only do you have to keep track of which positions were occupied,
but you have also have to keep track of which object occupied each 
position.  By default, the stimuli are differentiated by color
(blue is number 1, green is number 2, yellow is number 3, and red is 
number 4), though they can also be differentiated by image.

With a high interference, Brain Workshop will try to generate stimuli 
in a pattern designed to trick humans.  If the amount of interference
is set to 0.25, for example, then Brain Workshop will create a trick 
trial 25% of the time.  Trick trials are where the current cue matches
the cue either N-1, N+1, or 2N trials ago, but not the one N trials 
ago.  In multiple-stimulus modes, Brain Workshop will also occasionally
take the positions for the different objects used N trials ago, swap
them, and use those.
